import React, { createContext, useContext, useState, useCallback } from 'react';

export interface StatusItem {
  id: string;
  label: string;
  count: number;
  variant: 'default' | 'secondary' | 'destructive' | 'outline' | 'success' | 'warning' | 'info';
  className?: string;
}

interface NotificationStatusContextType {
  statusItems: StatusItem[];
  updateStatusCount: (id: string, count: number) => void;
  updateMultipleStatus: (updates: { id: string; count: number }[]) => void;
  resetAllCounts: () => void;
}

const NotificationStatusContext = createContext<NotificationStatusContextType | undefined>(undefined);

// Default status items configuration with theme-aware styling
const defaultStatusItems: StatusItem[] = [
  {
    id: 'websocket',
    label: 'Web Socket',
    count: 0,
    variant: 'info',
    className: 'status-items shadow-sm'
  },
  {
    id: 'trigger-waiting',
    label: 'Trigger Waiting',
    count: 0,
    variant: 'warning',
    className: 'status-items shadow-sm'
  },
  {
    id: 'lobby',
    label: 'Lobby',
    count: 0,
    variant: 'info',
    className: 'status-items shadow-sm'
  },
  {
    id: 'active',
    label: 'Active',
    count: 2,
    variant: 'success',
    className: 'status-items shadow-sm'
  },
  {
    id: 'hold',
    label: 'Hold',
    count: 0,
    variant: 'warning',
    className: 'status-items shadow-sm'
  },
  {
    id: 'problem',
    label: 'Problem',
    count: 0,
    variant: 'destructive',
    className: 'status-items shadow-sm'
  }
];

interface NotificationStatusProviderProps {
  children: React.ReactNode;
  initialStatusItems?: StatusItem[];
}

export function NotificationStatusProvider({
  children,
  initialStatusItems = defaultStatusItems
}: NotificationStatusProviderProps) {
  const [statusItems, setStatusItems] = useState<StatusItem[]>(initialStatusItems);

  const updateStatusCount = useCallback((id: string, count: number) => {
    setStatusItems(prev =>
      prev.map(item =>
        item.id === id ? { ...item, count } : item
      )
    );
  }, []);

  const updateMultipleStatus = useCallback((updates: { id: string; count: number }[]) => {
    setStatusItems(prev =>
      prev.map(item => {
        const update = updates.find(u => u.id === item.id);
        return update ? { ...item, count: update.count } : item;
      })
    );
  }, []);

  const resetAllCounts = useCallback(() => {
    setStatusItems(prev =>
      prev.map(item => ({ ...item, count: 0 }))
    );
  }, []);

  const value = {
    statusItems,
    updateStatusCount,
    updateMultipleStatus,
    resetAllCounts
  };

  return (
    <NotificationStatusContext.Provider value={value}>
      {children}
    </NotificationStatusContext.Provider>
  );
}

export function useNotificationStatus() {
  const context = useContext(NotificationStatusContext);
  if (context === undefined) {
    throw new Error('useNotificationStatus must be used within a NotificationStatusProvider');
  }
  return context;
}

// Export default status items for reference
export { defaultStatusItems };
