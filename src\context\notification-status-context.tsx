import React, { createContext, useContext, useState, useCallback } from 'react';

export interface StatusItem {
  id: string;
  label: string;
  count: number;
  variant: 'default' | 'secondary' | 'destructive' | 'outline' | 'success' | 'warning' | 'info';
  className?: string;
}

interface NotificationStatusContextType {
  statusItems: StatusItem[];
  updateStatusCount: (id: string, count: number) => void;
  updateMultipleStatus: (updates: { id: string; count: number }[]) => void;
  resetAllCounts: () => void;
}

const NotificationStatusContext = createContext<NotificationStatusContextType | undefined>(undefined);

// Default status items configuration
const defaultStatusItems: StatusItem[] = [
  {
    id: 'websocket',
    label: 'Web Socket',
    count: 0,
    variant: 'info',
    className: 'bg-blue-500 hover:bg-blue-600 text-white'
  },
  {
    id: 'trigger-waiting',
    label: 'Trigger Waiting',
    count: 0,
    variant: 'warning',
    className: 'bg-orange-500 hover:bg-orange-600 text-white'
  },
  {
    id: 'lobby',
    label: 'Lobby',
    count: 0,
    variant: 'info',
    className: 'bg-blue-600 hover:bg-blue-700 text-white'
  },
  {
    id: 'active',
    label: 'Active',
    count: 2,
    variant: 'success',
    className: 'bg-green-500 hover:bg-green-600 text-white'
  },
  {
    id: 'hold',
    label: 'Hold',
    count: 0,
    variant: 'warning',
    className: 'bg-yellow-500 hover:bg-yellow-600 text-white'
  },
  {
    id: 'problem',
    label: 'Problem',
    count: 0,
    variant: 'destructive',
    className: 'bg-red-500 hover:bg-red-600 text-white'
  }
];

interface NotificationStatusProviderProps {
  children: React.ReactNode;
  initialStatusItems?: StatusItem[];
}

export function NotificationStatusProvider({ 
  children, 
  initialStatusItems = defaultStatusItems 
}: NotificationStatusProviderProps) {
  const [statusItems, setStatusItems] = useState<StatusItem[]>(initialStatusItems);

  const updateStatusCount = useCallback((id: string, count: number) => {
    setStatusItems(prev => 
      prev.map(item => 
        item.id === id ? { ...item, count } : item
      )
    );
  }, []);

  const updateMultipleStatus = useCallback((updates: { id: string; count: number }[]) => {
    setStatusItems(prev => 
      prev.map(item => {
        const update = updates.find(u => u.id === item.id);
        return update ? { ...item, count: update.count } : item;
      })
    );
  }, []);

  const resetAllCounts = useCallback(() => {
    setStatusItems(prev => 
      prev.map(item => ({ ...item, count: 0 }))
    );
  }, []);

  const value = {
    statusItems,
    updateStatusCount,
    updateMultipleStatus,
    resetAllCounts
  };

  return (
    <NotificationStatusContext.Provider value={value}>
      {children}
    </NotificationStatusContext.Provider>
  );
}

export function useNotificationStatus() {
  const context = useContext(NotificationStatusContext);
  if (context === undefined) {
    throw new Error('useNotificationStatus must be used within a NotificationStatusProvider');
  }
  return context;
}

// Export default status items for reference
export { defaultStatusItems };
