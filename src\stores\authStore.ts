import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import Cookies from 'js-cookie'

const ACCESS_TOKEN = 'ACCESS_TOKEN'

interface AuthUser {
  accountNo: string
  email: string
  role: string[]
  exp: number
}

interface AuthSlice {
  user: AuthUser | null
  setUser: (user: AuthUser | null) => void
  accessToken: string
  setAccessToken: (accessToken: string) => void
  resetAccessToken: () => void
  reset: () => void
}

interface UIState {
  showSpinner: boolean
  setShowSpinner: (show: boolean) => void
}

type StoreState = {
  auth: AuthSlice
  ui: UIState
}

export const useAuthStore = create<StoreState>()(
  persist(
    (set) => ({
      auth: {
        user: null,
        accessToken: Cookies.get(ACCESS_TOKEN) ?? '',

        setUser: (user) =>
          set((state) => ({ ...state, auth: { ...state.auth, user } })),

        setAccessToken: (accessToken) =>
          set((state) => {
            Cookies.set(ACCESS_TOKEN, accessToken)
            return {
              ...state,
              auth: { ...state.auth, accessToken },
            }
          }),

        resetAccessToken: () =>
          set((state) => {
            Cookies.remove(ACCESS_TOKEN)
            return { ...state, auth: { ...state.auth, accessToken: '' } }
          }),

        reset: () =>
          set((state) => {
            Cookies.remove(ACCESS_TOKEN)
            localStorage.removeItem('rememberedEmail')
            localStorage.removeItem('rememberMe')
            return {
              ...state,
              auth: {
                ...state.auth,
                user: null,
                accessToken: ''
              },
            }
          }),
      },

      ui: {
        showSpinner: false,
        setShowSpinner: (show) =>
          set((state) => ({ ...state, ui: { ...state.ui, showSpinner: show } })),
      },
    }),
    {
      name: 'auth-storage', // Key used in localStorage
      partialize: (state) => ({
        auth: {
          user: state.auth.user,
          accessToken: state.auth.accessToken,
        },
      }),
    }
  )
)


export const getAccessToken = () => {
  const token = Cookies.get(ACCESS_TOKEN)
  return token ?? ''
} 
