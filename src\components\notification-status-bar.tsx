import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { useNotificationStatus } from '@/context/notification-status-context';

interface NotificationStatusBarProps {
  className?: string;
}

export function NotificationStatusBar({ className }: NotificationStatusBarProps) {
  const { statusItems } = useNotificationStatus();

  return (
    <div className={cn(
      "flex items-center space-x-2 px-2 py-1 bg-blue-500 text-white rounded-sm",
      className
    )}>
      {statusItems.map((item) => (
        <div
          key={item.id}
          className={cn(
            "flex items-center space-x-1 px-2 py-1 rounded text-xs font-medium cursor-pointer transition-colors",
            item.className
          )}
        >
          <span>{item.label}</span>
          <Badge
            variant="secondary"
            className="bg-white/20 text-white hover:bg-white/30 text-xs min-w-[20px] h-5 flex items-center justify-center"
          >
            {item.count}
          </Badge>
        </div>
      ))}
    </div>
  );
}
