import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useNotificationStatus } from '@/context/notification-status-context';
import { NotificationStatusManager, STATUS_IDS, handleSessionStatusUpdate } from '@/utils/notification-status-manager';

/**
 * Example component showing how to use the notification status system
 * This component demonstrates various ways to update the status counts
 */
export function NotificationStatusExample() {
  const { statusItems, updateStatusCount, updateMultipleStatus, resetAllCounts } = useNotificationStatus();

  // Example: Update individual status
  const handleUpdateWebSocket = () => {
    updateStatusCount(STATUS_IDS.WEBSOCKET, Math.floor(Math.random() * 5));
  };

  const handleUpdateActive = () => {
    updateStatusCount(STATUS_IDS.ACTIVE, Math.floor(Math.random() * 10));
  };

  // Example: Update multiple statuses at once
  const handleUpdateMultiple = () => {
    const updates = [
      { id: STATUS_IDS.LOBBY, count: Math.floor(Math.random() * 8) },
      { id: STATUS_IDS.HOLD, count: Math.floor(Math.random() * 3) },
      { id: STATUS_IDS.PROBLEM, count: Math.floor(Math.random() * 2) }
    ];
    updateMultipleStatus(updates);
  };

  // Example: Simulate API response
  const handleSimulateApiResponse = () => {
    const mockApiResponse = {
      websocketConnected: true,
      triggerWaiting: Math.floor(Math.random() * 5),
      lobby: Math.floor(Math.random() * 15),
      active: Math.floor(Math.random() * 20),
      hold: Math.floor(Math.random() * 3),
      problem: Math.floor(Math.random() * 2)
    };

    const updates = handleSessionStatusUpdate(mockApiResponse);
    updateMultipleStatus(updates);
  };

  // Example: Using the NotificationStatusManager utility
  const handleIncrementTriggerWaiting = () => {
    const currentCount = statusItems.find(item => item.id === STATUS_IDS.TRIGGER_WAITING)?.count || 0;
    const update = NotificationStatusManager.updateTriggerWaiting(currentCount + 1);
    updateStatusCount(update.id, update.count);
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Notification Status Example</CardTitle>
        <CardDescription>
          This component demonstrates how to update the notification status bar from anywhere in your application.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Current Status Display */}
        <div className="grid grid-cols-2 gap-4">
          {statusItems.map((item) => (
            <div key={item.id} className="flex justify-between items-center p-2 border rounded">
              <span className="font-medium">{item.label}:</span>
              <span className="font-bold">{item.count}</span>
            </div>
          ))}
        </div>

        {/* Control Buttons */}
        <div className="space-y-2">
          <div className="flex flex-wrap gap-2">
            <Button onClick={handleUpdateWebSocket} variant="outline" size="sm">
              Update WebSocket
            </Button>
            <Button onClick={handleUpdateActive} variant="outline" size="sm">
              Update Active
            </Button>
            <Button onClick={handleIncrementTriggerWaiting} variant="outline" size="sm">
              +1 Trigger Waiting
            </Button>
          </div>

          <div className="flex flex-wrap gap-2">
            <Button onClick={handleUpdateMultiple} variant="secondary" size="sm">
              Update Multiple
            </Button>
            <Button onClick={handleSimulateApiResponse} variant="secondary" size="sm">
              Simulate API Response
            </Button>
          </div>

          <div className="flex justify-center">
            <Button onClick={resetAllCounts} variant="destructive" size="sm">
              Reset All Counts
            </Button>
          </div>
        </div>

        {/* Usage Instructions */}
        <div className="mt-6 p-4 bg-muted rounded-lg">
          <h4 className="font-semibold mb-2">How to use in your components:</h4>
          <pre className="text-sm overflow-x-auto">
{`// Import the hook
import { useNotificationStatus } from '@/context/notification-status-context';

// In your component
const { updateStatusCount, updateMultipleStatus } = useNotificationStatus();

// Update single status
updateStatusCount('active', 5);

// Update multiple statuses
updateMultipleStatus([
  { id: 'lobby', count: 3 },
  { id: 'problem', count: 1 }
]);`}
          </pre>
        </div>
      </CardContent>
    </Card>
  );
}
