import { getModelProfileImages } from '../../api'
import { S3_BASE_URL } from '@/features/members/utils/utilities'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { IconPhoto, IconEye } from '@tabler/icons-react'
import { useState } from 'react'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'

interface ModelImagesGalleryProps {
  modelId: string
}

export default function ModelImagesGallery({ modelId }: ModelImagesGalleryProps) {
  const { data: images = [], isLoading } = getModelProfileImages(modelId)
  const [selectedImage, setSelectedImage] = useState<string | null>(null)

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg border p-6">
        <div className="flex items-center gap-2 mb-4">
          <IconPhoto className="h-5 w-5 text-gray-600" />
          <h3 className="text-lg font-semibold">Profile Images</h3>
        </div>
        <div className="text-center text-gray-500">Loading images...</div>
      </div>
    )
  }

  if (!images || images.length === 0) {
    return (
      <div className="bg-white rounded-lg border p-6">
        <div className="flex items-center gap-2 mb-4">
          <IconPhoto className="h-5 w-5 text-gray-600" />
          <h3 className="text-lg font-semibold">Profile Images</h3>
        </div>
        <div className="text-center text-gray-500 py-8">
          <IconPhoto className="h-12 w-12 mx-auto mb-2 text-gray-300" />
          <p>No images uploaded yet</p>
        </div>
      </div>
    )
  }

  return (
    <>
      <div className="bg-white rounded-lg border p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <IconPhoto className="h-5 w-5 text-gray-600" />
            <h3 className="text-lg font-semibold">Profile Images</h3>
            <Badge variant="secondary">{images.length}</Badge>
          </div>
        </div>

        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
          {images.map((image: any, index: number) => (
            <div key={image.id || index} className="relative group">
              <div className="aspect-square rounded-lg overflow-hidden bg-gray-100">
                <Avatar className="w-full h-full rounded-lg">
                  <AvatarImage
                    src={S3_BASE_URL + image.imagePath}
                    alt={`Profile image ${index + 1}`}
                    className="object-cover w-full h-full"
                  />
                  <AvatarFallback className="rounded-lg">
                    <IconPhoto className="h-8 w-8 text-gray-400" />
                  </AvatarFallback>
                </Avatar>
                
                {/* Default badge */}
                {image.isDefault && (
                  <div className="absolute top-2 left-2">
                    <Badge variant="default" className="text-xs">Default</Badge>
                  </div>
                )}

                {/* Hover overlay */}
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-200 flex items-center justify-center">
                  <Button
                    variant="secondary"
                    size="sm"
                    className="opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                    onClick={() => setSelectedImage(S3_BASE_URL + image.imagePath)}
                  >
                    <IconEye className="h-4 w-4 mr-1" />
                    View
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Image Preview Dialog */}
      <Dialog open={!!selectedImage} onOpenChange={() => setSelectedImage(null)}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Profile Image</DialogTitle>
          </DialogHeader>
          <div className="flex justify-center">
            <img
              src={selectedImage || ''}
              alt="Profile image preview"
              className="max-w-full max-h-[70vh] object-contain rounded-lg"
            />
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}
