import { useMutation, useQuery } from "@tanstack/react-query";
import { API_ENDPOINTS } from "./api-endpoints";
import { apiClient } from "@/api/apiClient";
import axios from "axios";


export const useGetModels = (params = {}) =>
    useQuery({
        queryFn: async () => {
            const response = await apiClient.get(API_ENDPOINTS.MODELS, {
                params,
            });
            return response?.data ?? {}; // return [] or {} as a fallback
        },
        queryKey: ["models-list"],
    });


export const addModelApi = () =>
    useMutation({
        mutationFn: async (payload: any) => {
            return await apiClient.post(API_ENDPOINTS.MODELS, payload);
        },
    });

export const updateModelApi = () =>
    useMutation({
        mutationFn: async (payload: any) => {
            return await apiClient.put(`${API_ENDPOINTS.MODELS}/${payload?.id}`, payload);
        },
    });

export const deleteModelApi = () =>
    useMutation({
        mutationFn: async (payload: any) => {
            return await apiClient.delete(`${API_ENDPOINTS.MODELS}/${payload?.id}`);
        },
    });

export const modelStatusChange = () =>
    useMutation({
        mutationFn: async (id: any) => {
            return await apiClient.get(`${API_ENDPOINTS.MODELS}/${id}${API_ENDPOINTS.MODELS_STATUS}`);
        },
    });


export const getModelDetails = (id: any = {}) =>
    useQuery({
        queryFn: async () => {
            if (typeof id === 'string') {
                const response = await apiClient.get(`${API_ENDPOINTS.MODELS}/${id}`)
                return response?.data ?? {}; // return [] or {} as a fallback
            }
            return {}

        },
        queryKey: ["model-details", id],
        enabled: !!id
    });

export const getPresignedUrl = () =>
    useMutation({
        mutationFn: async (params: any) => {
            return await apiClient.get(`${API_ENDPOINTS.S3_PRESIGNED_URL}`, { params });
        },
    });

export const updateProfilePictureApi = () =>
    useMutation({
        mutationFn: async (payload: any) => {
            return await apiClient.put(`${API_ENDPOINTS.MODELS}/${payload?.id}/profile`, { imagePath: payload.imagePath });
        },
    });

export const uploadFileToS3 = () =>
    useMutation({
        mutationFn: async ({ url, file }: any) => {
            return await axios.put(url, file);
        },
    });

// API for managing model profile images
export const addModelProfileImageApi = () =>
    useMutation({
        mutationFn: async (payload: any) => {
            return await apiClient.put(`${API_ENDPOINTS.MODELS}/${payload?.modelId}/album-upload`, { images: [payload.imagePath] });
        },
    });

export const setDefaultModelImageApi = () =>
    useMutation({
        mutationFn: async (payload: any) => {
            return await apiClient.put(`${API_ENDPOINTS.MODELS}/${payload?.modelId}/avatar`, { imagePath: payload.imagePath });
        },
    });

export const deleteModelImageApi = () =>
    useMutation({
        mutationFn: async (payload: any) => {
            return await apiClient.delete(`${API_ENDPOINTS.MODELS}/${payload?.modelId}/image/${payload?.imageId}`);
        },
    });

export const getModelProfileImages = (modelId: any) =>
    useQuery({
        queryFn: async () => {
            if (typeof modelId === 'string') {
                const response = await apiClient.get(`${API_ENDPOINTS.MODELS}/${modelId}/profile-images`)
                return response?.data ?? []; // return [] as a fallback
            }
            return []
        },
        queryKey: ["model-profile-images", modelId],
        enabled: !!modelId
    });


