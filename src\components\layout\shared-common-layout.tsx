import { Header } from "@/components/layout/header";
import { ProfileDropdown } from "@/components/profile-dropdown";
import { ThemeSwitch } from "@/components/theme-switch";
import { NotificationStatusBar } from "@/components/notification-status-bar";
import { Outlet } from "@tanstack/react-router";

export default function SharedCommonLayout() {
  return (
    <>
      {/* ===== Top Heading ===== */}
      <Header>
        <div className="ml-auto flex items-center space-x-4">
          <NotificationStatusBar />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <Outlet />
    </>
  );
}
